import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AIAgent from './AIAgent';
import QuestionnaireLoader from './QuestionnaireLoader';
import { saveQuestionnaireResponses, getAllCompletedQuestionnaires, getQuestionnaireResponses } from '../supabase/client';
import { useAuth } from '../context/AuthContext';

function CompetitionAnalysis() {
  const [activeQuestionnaire, setActiveQuestionnaire] = useState(null); // null | 'competitorProfiling' | 'competitivePositioning' | 'swotAnalysis' | 'marketShareAnalysis'
  const [submissionResult, setSubmissionResult] = useState(null);
  const [generatingStrategy, setGeneratingStrategy] = useState(false);
  const navigate = useNavigate();
  const { user } = useAuth();

  // Track which questionnaires have been completed
  const [completedQuestionnaires, setCompletedQuestionnaires] = useState({
    competitorProfiling: false,
    competitivePositioning: false,
    swotAnalysis: false,
    marketShareAnalysis: false
  });

  // Track which questionnaires have actual responses saved
  const [actualCompletedQuestionnaires, setActualCompletedQuestionnaires] = useState({
    competitorProfiling: false,
    competitivePositioning: false,
    swotAnalysis: false,
    marketShareAnalysis: false
  });

  // Loading state for initial data fetch
  const [loading, setLoading] = useState(true);

  // Load completed questionnaires from Supabase on component mount
  useEffect(() => {
    const loadCompletedQuestionnaires = async () => {
      try {
        setLoading(true);
        // Only load data if user is authenticated
        if (user) {
          const { completionStatus, error } = await getAllCompletedQuestionnaires(true); // true = user-specific data

          if (error) {
            console.error('Error loading completed questionnaires:', error);
            fallbackToLocalStorage();
          } else if (completionStatus) {
            // Filter for competition analysis questionnaires
            const competitionStatus = {
              competitorProfiling: completionStatus.competitorProfiling || false,
              competitivePositioning: completionStatus.competitivePositioning || false,
              swotAnalysis: completionStatus.swotAnalysis || false,
              marketShareAnalysis: completionStatus.marketShareAnalysis || false
            };
            setCompletedQuestionnaires(competitionStatus);
            setActualCompletedQuestionnaires(competitionStatus); // In Supabase, completed = has responses
          }
        } else {
          // Not authenticated, use localStorage
          fallbackToLocalStorage();
        }
      } catch (err) {
        console.error('Error in loadCompletedQuestionnaires:', err);
        fallbackToLocalStorage();
      } finally {
        setLoading(false);
      }
    };

    const fallbackToLocalStorage = () => {
      // Fall back to localStorage
      const saved = localStorage.getItem('completedQuestionnaires_competition');
      if (saved) {
        const localData = JSON.parse(saved);
        setCompletedQuestionnaires(localData);

        // Check localStorage for actual responses
        setActualCompletedQuestionnaires({
          competitorProfiling: !!localStorage.getItem('questionnaire_responses_competitorProfiling'),
          competitivePositioning: !!localStorage.getItem('questionnaire_responses_competitivePositioning'),
          swotAnalysis: !!localStorage.getItem('questionnaire_responses_swotAnalysis'),
          marketShareAnalysis: !!localStorage.getItem('questionnaire_responses_marketShareAnalysis')
        });
      }
    };

    loadCompletedQuestionnaires();
  }, [user]);

  // Helper function to get questionnaire display name
  const getQuestionnaireNameFromKey = (key) => {
    const nameMap = {
      competitorProfiling: 'Competitor Profiling Questionnaire',
      competitivePositioning: 'Competitive Positioning Questionnaire',
      swotAnalysis: 'SWOT Analysis Questionnaire',
      marketShareAnalysis: 'Market Share Analysis Questionnaire'
    };
    return nameMap[key] || key;
  };

  // Questionnaire configurations
  const questionnaireConfigs = {
    competitorProfiling: {
      title: "Competitor Profiling Questionnaire",
      description: "This questionnaire helps in creating detailed profiles of your main competitors in the spiritual fine jewelry market.",
      files: ['competitor-profiling-questionnaire.yaml'],
      defaultFile: 'competitor-profiling-questionnaire.yaml'
    },
    competitivePositioning: {
      title: "Competitive Positioning Questionnaire",
      description: "This questionnaire helps determine your brand's unique position in the spiritual fine jewelry market relative to your competitors.",
      files: ['competitive-positioning-questionnaire.yaml'],
      defaultFile: 'competitive-positioning-questionnaire.yaml'
    },
    swotAnalysis: {
      title: "SWOT Analysis Questionnaire",
      description: "This questionnaire helps you conduct a comprehensive SWOT analysis for your spiritual fine jewelry business.",
      files: ['swot-analysis-questionnaire.yaml'],
      defaultFile: 'swot-analysis-questionnaire.yaml'
    },
    marketShareAnalysis: {
      title: "Market Share Analysis Questionnaire",
      description: "This questionnaire helps you analyze market share distribution and identify growth opportunities.",
      files: ['market-share-analysis-questionnaire.yaml'],
      defaultFile: 'market-share-analysis-questionnaire.yaml'
    }
  };

  // Handle questionnaire submission
  const handleSubmit = async (data) => {
    console.log('Questionnaire submitted:', data);

    // Mark the current questionnaire as completed
    if (activeQuestionnaire) {
      const updatedCompletedQuestionnaires = {
        ...completedQuestionnaires,
        [activeQuestionnaire]: true
      };

      // Save to state and localStorage as backup
      setCompletedQuestionnaires(updatedCompletedQuestionnaires);
      localStorage.setItem('completedQuestionnaires_competition', JSON.stringify(updatedCompletedQuestionnaires));

      // Get questionnaire name for display
      const questionnaireName = getQuestionnaireNameFromKey(activeQuestionnaire);

      // Prepare response data
      const responseData = {
        questionnaire: questionnaireName,
        responses: data,
        timestamp: new Date().toISOString(),
        user_id: user?.id || null // Add user ID if authenticated
      };

      console.log('Saving questionnaire responses:', responseData);

      // Save to Supabase if user is authenticated
      if (user) {
        try {
          const { error } = await saveQuestionnaireResponses(
            activeQuestionnaire,
            questionnaireName,
            data,
            user.id
          );

          if (error) {
            console.error('Error saving to Supabase:', error);
            // Fall back to localStorage
            localStorage.setItem(`questionnaire_responses_${activeQuestionnaire}`, JSON.stringify(responseData));
          } else {
            console.log('Successfully saved to Supabase');
            // Update the actualCompletedQuestionnaires state
            setActualCompletedQuestionnaires(prev => ({
              ...prev,
              [activeQuestionnaire]: true
            }));
          }

          // Store in sessionStorage for immediate viewing
          sessionStorage.setItem('questionnaire_view_responses', JSON.stringify(responseData));
        } catch (err) {
          console.error('Error in handleSubmit:', err);
          // Fall back to localStorage
          localStorage.setItem(`questionnaire_responses_${activeQuestionnaire}`, JSON.stringify(responseData));
        }
      } else {
        // Not authenticated, use localStorage only
        localStorage.setItem(`questionnaire_responses_${activeQuestionnaire}`, JSON.stringify(responseData));

        // Update the actualCompletedQuestionnaires state
        setActualCompletedQuestionnaires(prev => ({
          ...prev,
          [activeQuestionnaire]: true
        }));

        // Store in sessionStorage for immediate viewing
        sessionStorage.setItem('questionnaire_view_responses', JSON.stringify(responseData));
      }
    }

    setSubmissionResult({
      success: true,
      message: 'Thank you for your submission!',
      data: data
    });
    setActiveQuestionnaire(null);
  };
  
  // Handle strategy generation
  const handleGenerateStrategy = (data) => {
    console.log('Generating strategy from data:', data);
    setGeneratingStrategy(true);

    // Simulate API call to generate strategy
    setTimeout(() => {
      setSubmissionResult({
        success: true,
        message: 'Your competitive analysis strategy has been generated!',
        data: data,
        isStrategy: true
      });
      setGeneratingStrategy(false);
      setActiveQuestionnaire(null);
    }, 2000);
  };

  // Toggle questionnaire visibility
  const toggleQuestionnaire = (questionnaireType) => {
    console.log('Toggling questionnaire:', questionnaireType, 'Current active:', activeQuestionnaire);
    setActiveQuestionnaire(activeQuestionnaire === questionnaireType ? null : questionnaireType);
  };
  const contextPrompt = `You are a competition analysis expert. Your task is to provide detailed, accurate, and helpful information about competitive analysis methodologies, frameworks, and best practices. Focus on providing actionable insights that can help businesses understand their competitors, identify competitive advantages, and develop effective competitive strategies.`;
  
  return (
    <div className="space-y-8">
      <div className="w-full max-w-4xl p-6 bg-white rounded shadow-md text-center mx-auto">
        <h2 className="raleway-title-h2 mb-4">Competition Analysis</h2>
        <p className="body-text mb-4">
          Tools and methodologies to analyze competitors and identify market opportunities.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          <div
            className={`p-4 border rounded-lg shadow-sm hover:shadow-md transition-all cursor-pointer ${activeQuestionnaire === 'competitorProfiling' ? 'ring-2 ring-red-400 bg-red-100' : 'bg-red-50 hover:bg-red-100'}`}
            onClick={() => toggleQuestionnaire('competitorProfiling')}
          >
            <h3 className="raleway-title-h3 mb-2 text-red-800">Competitor Profiling</h3>
            <p className="body-text">Create detailed profiles of your main competitors.</p>
          </div>
          <div
            className={`p-4 border rounded-lg shadow-sm hover:shadow-md transition-all cursor-pointer ${activeQuestionnaire === 'swotAnalysis' ? 'ring-2 ring-red-400 bg-red-100' : 'bg-red-50 hover:bg-red-100'}`}
            onClick={() => toggleQuestionnaire('swotAnalysis')}
          >
            <h3 className="raleway-title-h3 mb-2 text-red-800">SWOT Analysis</h3>
            <p className="body-text">Analyze strengths, weaknesses, opportunities, and threats in the market.</p>
          </div>
          <div
            className={`p-4 border rounded-lg shadow-sm hover:shadow-md transition-all cursor-pointer ${activeQuestionnaire === 'competitivePositioning' ? 'ring-2 ring-red-400 bg-red-100' : 'bg-red-50 hover:bg-red-100'}`}
            onClick={() => toggleQuestionnaire('competitivePositioning')}
          >
            <h3 className="raleway-title-h3 mb-2 text-red-800">Competitive Positioning</h3>
            <p className="body-text">Determine your position in the market relative to competitors.</p>
          </div>
          <div
            className={`p-4 border rounded-lg shadow-sm hover:shadow-md transition-all cursor-pointer ${activeQuestionnaire === 'marketShareAnalysis' ? 'ring-2 ring-red-400 bg-red-100' : 'bg-red-50 hover:bg-red-100'}`}
            onClick={() => toggleQuestionnaire('marketShareAnalysis')}
          >
            <h3 className="raleway-title-h3 mb-2 text-red-800">Market Share Analysis</h3>
            <p className="body-text">Analyze market share distribution and identify growth opportunities.</p>
          </div>
        </div>

        {/* Questionnaire Completion Status */}
        <div className="mt-8 p-4 bg-gray-50 rounded-lg">
          <h3 className="raleway-title-h3 mb-4 text-gray-800">Questionnaire Completion Status</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center justify-between p-3 bg-white rounded border">
              <span className="body-text">Competitor Profiling</span>
              <span className={`px-2 py-1 rounded text-xs font-medium ${
                actualCompletedQuestionnaires.competitorProfiling
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-600'
              }`}>
                {actualCompletedQuestionnaires.competitorProfiling ? 'Completed' : 'Not Started'}
              </span>
            </div>
            <div className="flex items-center justify-between p-3 bg-white rounded border">
              <span className="body-text">Competitive Positioning</span>
              <span className={`px-2 py-1 rounded text-xs font-medium ${
                actualCompletedQuestionnaires.competitivePositioning
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-600'
              }`}>
                {actualCompletedQuestionnaires.competitivePositioning ? 'Completed' : 'Not Started'}
              </span>
            </div>
            <div className="flex items-center justify-between p-3 bg-white rounded border">
              <span className="body-text">SWOT Analysis</span>
              <span className={`px-2 py-1 rounded text-xs font-medium ${
                actualCompletedQuestionnaires.swotAnalysis
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-600'
              }`}>
                {actualCompletedQuestionnaires.swotAnalysis ? 'Completed' : 'Not Started'}
              </span>
            </div>
            <div className="flex items-center justify-between p-3 bg-white rounded border">
              <span className="body-text">Market Share Analysis</span>
              <span className={`px-2 py-1 rounded text-xs font-medium ${
                actualCompletedQuestionnaires.marketShareAnalysis
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-600'
              }`}>
                {actualCompletedQuestionnaires.marketShareAnalysis ? 'Completed' : 'Not Started'}
              </span>
            </div>
          </div>

          {/* Strategy Generation Section */}
          <div className="mt-8 p-6 bg-white rounded-lg border border-red-200 shadow-md">
            <h4 className="raleway-title-h4 mb-3 text-red-800">GENERATE COMPETITIVE ANALYSIS STRATEGY</h4>
            <p className="body-text mb-4">
              Ready to turn your questionnaire responses into an actionable competitive analysis strategy?
              Click the button below to generate a comprehensive strategy tailored to your business needs.
            </p>
            <div className="flex flex-wrap gap-4">
              <button
                className="px-6 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 transition flex items-center"
                onClick={() => {
                  // Store the responses in sessionStorage for the strategy page
                  const responseData = {
                    questionnaire: 'Competition Analysis Questionnaires',
                    responses: completedQuestionnaires,
                    timestamp: new Date().toISOString()
                  };
                  sessionStorage.setItem('questionnaire_responses', JSON.stringify(responseData));
                  // Navigate to the strategy page
                  navigate('/strategy');
                }}
                disabled={!Object.values(completedQuestionnaires).some(value => value)}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                Generate Strategy
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Questionnaire Section */}
      {activeQuestionnaire && (
        <div className="mb-8 p-6 bg-white rounded-lg border border-gray-200 shadow-md">
          <div className="flex justify-end mb-4">
            <button
              onClick={() => setActiveQuestionnaire(null)}
              className="text-gray-500 hover:text-gray-700"
              aria-label="Close questionnaire"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <QuestionnaireLoader
            key={activeQuestionnaire}
            title={questionnaireConfigs[activeQuestionnaire].title}
            description={questionnaireConfigs[activeQuestionnaire].description}
            specificQuestionnaires={questionnaireConfigs[activeQuestionnaire].files}
            defaultQuestionnaire={questionnaireConfigs[activeQuestionnaire].defaultFile}
            onSubmit={handleSubmit}
            onGenerateStrategy={handleGenerateStrategy}
            showLocalSave={false}
            hideGenerateStrategyButton={true}
            hideQuestionnaireSelector={true}
          />
        </div>
      )}

      {/* Questionnaire Success Message Popup */}
      {submissionResult && (
        <div className="fixed inset-0 flex items-center justify-center z-50">
          <div className="absolute inset-0 bg-black opacity-30"></div>
          <div className="bg-white p-6 rounded-lg shadow-xl border border-green-200 z-10 max-w-md w-full mx-4">
            <div className="flex items-center justify-center mb-4 text-green-500">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold mb-2 text-center text-green-700">
              {submissionResult.message}
            </h2>
            {submissionResult.isStrategy ? (
              <div>
                <p className="mb-4">
                  Based on your responses to the "{submissionResult.data.questionnaire}" questionnaire,
                  we've generated a customized competitive analysis strategy for your business.
                </p>
                <div className="p-4 bg-white rounded border border-blue-100 mb-4">
                  <h3 className="raleway-title-h3 mb-2">Your Competitive Analysis Strategy</h3>
                  <p className="body-text mb-2">This strategy is tailored to help you understand and outperform your competitors:</p>
                  <ul className="list-disc pl-5 space-y-2 body-text">
                    <li>Conduct a SWOT analysis for each of your top 3 competitors</li>
                    <li>Analyze pricing strategies across your market segment</li>
                    <li>Evaluate competitors' marketing channels and messaging</li>
                    <li>Identify gaps in competitors' product/service offerings</li>
                    <li>Develop a competitive positioning strategy to differentiate your business</li>
                  </ul>
                </div>
              </div>
            ) : (
              <p className="mb-4">
                We've received your responses for the "{submissionResult.data.questionnaire}" questionnaire.
                Our team will analyze your information and provide tailored competition analysis recommendations.
              </p>
            )}
            <button
              className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
              onClick={() => setSubmissionResult(null)}
            >
              Close
            </button>
          </div>
        </div>
      )}

      {/* Strategy Generation Loading */}
      {generatingStrategy && (
        <div className="bg-blue-50 p-6 rounded-lg shadow-md border border-blue-200 mb-8 text-center">
          <div className="animate-pulse flex flex-col items-center">
            <svg className="animate-spin h-10 w-10 text-blue-600 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <h2 className="text-xl font-semibold mb-2 text-blue-700">Generating Your Strategy</h2>
            <p className="body-text">We're analyzing your responses and creating a customized competitive analysis strategy...</p>
          </div>
        </div>
      )}

      {/* Competition Analysis AI Agent */}
      <AIAgent
        title="Competition Analysis Assistant"
        description="Ask questions about competitive analysis frameworks, tools, and strategies."
        contextPrompt={contextPrompt}
      />
    </div>
  );
}

export default CompetitionAnalysis;
                <button
                  className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition flex items-center"
                  onClick={async () => {
                    try {
                      // First try to get responses from Supabase
                      const { data, error } = await getQuestionnaireResponses('competitorProfiling');

                      if (error || !data) {
                        console.error('Error fetching from Supabase:', error);
                        // Fall back to localStorage
                        const storedData = localStorage.getItem('questionnaire_responses_competitorProfiling');
                        if (storedData) {
                          sessionStorage.setItem('questionnaire_view_responses', storedData);
                          navigate('/responses');
                        } else {
                          throw new Error('No data found in localStorage either');
                        }
                      } else {
                        // Format the data for ResponseView component
                        const formattedData = {
                          questionnaire: data.questionnaire_name,
                          responses: data.responses,
                          timestamp: data.created_at
                        };
                        sessionStorage.setItem('questionnaire_view_responses', JSON.stringify(formattedData));
                        navigate('/responses');
                      }
                    } catch (err) {
                      console.error('Error viewing responses:', err);
                      setSubmissionResult({
                        success: false,
                        message: 'Error loading responses. Please try again.',
                        data: null
                      });
                      setTimeout(() => {
                        setSubmissionResult(null);
                      }, 3000);
                    }
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  View Responses
                </button>
              )}
              {completedQuestionnaires.competitorProfiling && !actualCompletedQuestionnaires.competitorProfiling && (
                <button
                  className="px-3 py-1 bg-gray-400 text-white text-sm rounded hover:bg-gray-500 transition flex items-center"
                  onClick={() => {
                    setSubmissionResult({
                      success: false,
                      message: 'No responses available. Please complete the Competitor Profiling questionnaire first.',
                      data: null
                    });
                    setTimeout(() => {
                      setSubmissionResult(null);
                    }, 3000);
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  No Responses
                </button>
              )}
            </div>

            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center">
                <div className="completion-indicator">
                  {completedQuestionnaires.competitivePositioning ? (
                    <div className="completion-badge bg-red-500">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  ) : (
                    <div className="completion-badge bg-gray-200">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                  )}
                </div>
                <div className="ml-3">
                  <h5 className="raleway-title-h5 text-red-800">Competitive Positioning</h5>
                  <p className="text-sm text-gray-600">{completedQuestionnaires.competitivePositioning ? "Completed" : "Not completed"}</p>
                </div>
              </div>
              {actualCompletedQuestionnaires.competitivePositioning && (
                <button
                  className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition flex items-center"
                  onClick={async () => {
                    try {
                      const { data, error } = await getQuestionnaireResponses('competitivePositioning');

                      if (error || !data) {
                        console.error('Error fetching from Supabase:', error);
                        const storedData = localStorage.getItem('questionnaire_responses_competitivePositioning');
                        if (storedData) {
                          sessionStorage.setItem('questionnaire_view_responses', storedData);
                          navigate('/responses');
                        } else {
                          throw new Error('No data found in localStorage either');
                        }
                      } else {
                        const formattedData = {
                          questionnaire: data.questionnaire_name,
                          responses: data.responses,
                          timestamp: data.created_at
                        };
                        sessionStorage.setItem('questionnaire_view_responses', JSON.stringify(formattedData));
                        navigate('/responses');
                      }
                    } catch (err) {
                      console.error('Error viewing responses:', err);
                      setSubmissionResult({
                        success: false,
                        message: 'Error loading responses. Please try again.',
                        data: null
                      });
                      setTimeout(() => {
                        setSubmissionResult(null);
                      }, 3000);
                    }
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  View Responses
                </button>
              )}
              {completedQuestionnaires.competitivePositioning && !actualCompletedQuestionnaires.competitivePositioning && (
                <button
                  className="px-3 py-1 bg-gray-400 text-white text-sm rounded hover:bg-gray-500 transition flex items-center"
                  onClick={() => {
                    setSubmissionResult({
                      success: false,
                      message: 'No responses available. Please complete the Competitive Positioning questionnaire first.',
                      data: null
                    });
                    setTimeout(() => {
                      setSubmissionResult(null);
                    }, 3000);
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  No Responses
                </button>
              )}
            </div>

            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center">
                <div className="completion-indicator">
                  {completedQuestionnaires.swotAnalysis ? (
                    <div className="completion-badge bg-red-500">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  ) : (
                    <div className="completion-badge bg-gray-200">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                  )}
                </div>
                <div className="ml-3">
                  <h5 className="raleway-title-h5 text-red-800">SWOT Analysis</h5>
                  <p className="text-sm text-gray-600">{completedQuestionnaires.swotAnalysis ? "Completed" : "Not completed"}</p>
                </div>
              </div>
              {actualCompletedQuestionnaires.swotAnalysis && (
                <button
                  className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition flex items-center"
                  onClick={async () => {
                    try {
                      const { data, error } = await getQuestionnaireResponses('swotAnalysis');

                      if (error || !data) {
                        console.error('Error fetching from Supabase:', error);
                        const storedData = localStorage.getItem('questionnaire_responses_swotAnalysis');
                        if (storedData) {
                          sessionStorage.setItem('questionnaire_view_responses', storedData);
                          navigate('/responses');
                        } else {
                          throw new Error('No data found in localStorage either');
                        }
                      } else {
                        const formattedData = {
                          questionnaire: data.questionnaire_name,
                          responses: data.responses,
                          timestamp: data.created_at
                        };
                        sessionStorage.setItem('questionnaire_view_responses', JSON.stringify(formattedData));
                        navigate('/responses');
                      }
                    } catch (err) {
                      console.error('Error viewing responses:', err);
                      setSubmissionResult({
                        success: false,
                        message: 'Error loading responses. Please try again.',
                        data: null
                      });
                      setTimeout(() => {
                        setSubmissionResult(null);
                      }, 3000);
                    }
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  View Responses
                </button>
              )}
              {completedQuestionnaires.swotAnalysis && !actualCompletedQuestionnaires.swotAnalysis && (
                <button
                  className="px-3 py-1 bg-gray-400 text-white text-sm rounded hover:bg-gray-500 transition flex items-center"
                  onClick={() => {
                    setSubmissionResult({
                      success: false,
                      message: 'No responses available. Please complete the SWOT Analysis questionnaire first.',
                      data: null
                    });
                    setTimeout(() => {
                      setSubmissionResult(null);
                    }, 3000);
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  No Responses
                </button>
              )}
            </div>

            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center">
                <div className="completion-indicator">
                  {completedQuestionnaires.marketShareAnalysis ? (
                    <div className="completion-badge bg-red-500">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  ) : (
                    <div className="completion-badge bg-gray-200">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                  )}
                </div>
                <div className="ml-3">
                  <h5 className="raleway-title-h5 text-red-800">Market Share Analysis</h5>
                  <p className="text-sm text-gray-600">{completedQuestionnaires.marketShareAnalysis ? "Completed" : "Not completed"}</p>
                </div>
              </div>
              {actualCompletedQuestionnaires.marketShareAnalysis && (
                <button
                  className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition flex items-center"
                  onClick={async () => {
                    try {
                      const { data, error } = await getQuestionnaireResponses('marketShareAnalysis');

                      if (error || !data) {
                        console.error('Error fetching from Supabase:', error);
                        const storedData = localStorage.getItem('questionnaire_responses_marketShareAnalysis');
                        if (storedData) {
                          sessionStorage.setItem('questionnaire_view_responses', storedData);
                          navigate('/responses');
                        } else {
                          throw new Error('No data found in localStorage either');
                        }
                      } else {
                        const formattedData = {
                          questionnaire: data.questionnaire_name,
                          responses: data.responses,
                          timestamp: data.created_at
                        };
                        sessionStorage.setItem('questionnaire_view_responses', JSON.stringify(formattedData));
                        navigate('/responses');
                      }
                    } catch (err) {
                      console.error('Error viewing responses:', err);
                      setSubmissionResult({
                        success: false,
                        message: 'Error loading responses. Please try again.',
                        data: null
                      });
                      setTimeout(() => {
                        setSubmissionResult(null);
                      }, 3000);
                    }
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  View Responses
                </button>
              )}
              {completedQuestionnaires.marketShareAnalysis && !actualCompletedQuestionnaires.marketShareAnalysis && (
                <button
                  className="px-3 py-1 bg-gray-400 text-white text-sm rounded hover:bg-gray-500 transition flex items-center"
                  onClick={() => {
                    setSubmissionResult({
                      success: false,
                      message: 'No responses available. Please complete the Market Share Analysis questionnaire first.',
                      data: null
                    });
                    setTimeout(() => {
                      setSubmissionResult(null);
                    }, 3000);
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  No Responses
                </button>
              )}
            </div>
          </div>

          {/* Strategy Generation Section */}
          <div className="mt-8 p-6 bg-white rounded-lg border border-red-200 shadow-md">
            <h4 className="raleway-title-h4 mb-3 text-red-800">GENERATE COMPETITIVE ANALYSIS STRATEGY</h4>
            <p className="body-text mb-4">
              Ready to turn your questionnaire responses into an actionable competitive analysis strategy?
              Click the button below to generate a comprehensive strategy tailored to your business needs.
            </p>
            <div className="flex flex-wrap gap-4">
              <button
                className="px-6 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 transition flex items-center"
                onClick={() => {
                  // Store the responses in sessionStorage for the strategy page
                  const responseData = {
                    questionnaire: 'Competition Analysis Questionnaires',
                    responses: completedQuestionnaires,
                    timestamp: new Date().toISOString()
                  };
                  sessionStorage.setItem('questionnaire_responses', JSON.stringify(responseData));
                  // Navigate to the strategy page
                  navigate('/strategy');
                }}
                disabled={!Object.values(completedQuestionnaires).some(value => value)}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                Generate Strategy
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Questionnaire Section */}
      {activeQuestionnaire && (
        <div className="mb-8 p-6 bg-white rounded-lg border border-gray-200 shadow-md">
          <div className="flex justify-end mb-4">
            <button
              onClick={() => setActiveQuestionnaire(null)}
              className="text-gray-500 hover:text-gray-700"
              aria-label="Close questionnaire"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <QuestionnaireLoader
            key={activeQuestionnaire}
            title={questionnaireConfigs[activeQuestionnaire].title}
            description={questionnaireConfigs[activeQuestionnaire].description}
            specificQuestionnaires={questionnaireConfigs[activeQuestionnaire].files}
            defaultQuestionnaire={questionnaireConfigs[activeQuestionnaire].defaultFile}
            onSubmit={handleSubmit}
            onGenerateStrategy={handleGenerateStrategy}
            showLocalSave={false}
            hideGenerateStrategyButton={true}
            hideQuestionnaireSelector={true}
          />
        </div>
      )}

      {/* Questionnaire Success Message Popup */}
      {submissionResult && (
        <div className="fixed inset-0 flex items-center justify-center z-50">
          <div className="absolute inset-0 bg-black opacity-30"></div>
          <div className="bg-white p-6 rounded-lg shadow-xl border border-green-200 z-10 max-w-md w-full mx-4">
            <div className="flex items-center justify-center mb-4 text-green-500">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold mb-2 text-center text-green-700">
              {submissionResult.message}
            </h2>
            {submissionResult.isStrategy ? (
              <div>
                <p className="mb-4">
                  Based on your responses to the "{submissionResult.data.questionnaire}" questionnaire,
                  we've generated a customized competitive analysis strategy for your business.
                </p>
                <div className="p-4 bg-white rounded border border-blue-100 mb-4">
                  <h3 className="raleway-title-h3 mb-2">Your Competitive Analysis Strategy</h3>
                  <p className="body-text mb-2">This strategy is tailored to help you understand and outperform your competitors:</p>
                  <ul className="list-disc pl-5 space-y-2 body-text">
                    <li>Conduct a SWOT analysis for each of your top 3 competitors</li>
                    <li>Analyze pricing strategies across your market segment</li>
                    <li>Evaluate competitors' marketing channels and messaging</li>
                    <li>Identify gaps in competitors' product/service offerings</li>
                    <li>Develop a competitive positioning strategy to differentiate your business</li>
                  </ul>
                </div>
              </div>
            ) : (
              <p className="mb-4">
                We've received your responses for the "{submissionResult.data.questionnaire}" questionnaire.
                Our team will analyze your information and provide tailored competition analysis recommendations.
              </p>
            )}
            <button
              className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
              onClick={() => setSubmissionResult(null)}
            >
              Close
            </button>
          </div>
        </div>
      )}
      
      {/* Strategy Generation Loading */}
      {generatingStrategy && (
        <div className="bg-blue-50 p-6 rounded-lg shadow-md border border-blue-200 mb-8 text-center">
          <div className="animate-pulse flex flex-col items-center">
            <svg className="animate-spin h-10 w-10 text-blue-600 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <h2 className="text-xl font-semibold mb-2 text-blue-700">Generating Your Strategy</h2>
            <p className="body-text">We're analyzing your responses and creating a customized competitive analysis strategy...</p>
          </div>
        </div>
      )}
      
      {/* Competition Analysis AI Agent */}
      <AIAgent 
        title="Competition Analysis Assistant" 
        description="Ask questions about competitive analysis frameworks, tools, and strategies."
        contextPrompt={contextPrompt}
      />
    </div>
  );
}

export default CompetitionAnalysis;
