import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import QuestionnaireLoader from './QuestionnaireLoader';

/**
 * QuestionnairePage component
 * 
 * An example page that demonstrates how to use the QuestionnaireLoader component
 * in your application pages.
 */
function QuestionnairePage() {
  const [submissionResult, setSubmissionResult] = useState(null);
  const navigate = useNavigate();

  // Handle questionnaire submission
  const handleSubmit = (data) => {
    console.log('Questionnaire submitted:', data);
    setSubmissionResult({
      success: true,
      message: 'Thank you for your submission!',
      data: data
    });
    
    // Here you could also:
    // - Send the data to an API
    // - Process the responses
    // - Navigate to another page
    // - Show a success message
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6 text-blue-800">Marketing Questionnaires</h1>
      
      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        <h2 className="text-xl font-semibold mb-4 text-blue-700">Complete a Marketing Research Questionnaire</h2>
        <p className="mb-4">
          Select a questionnaire from the dropdown below to help us understand your business needs
          and develop targeted marketing strategies.
        </p>
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-6">
          <h3 className="font-semibold text-blue-800 mb-2">Benefits of Completing the Questionnaire:</h3>
          <ul className="list-disc pl-5 space-y-1">
            <li>Gain insights into your current marketing position</li>
            <li>Identify opportunities for improvement</li>
            <li>Receive tailored recommendations based on your responses</li>
            <li>Establish clear metrics for measuring success</li>
            <li>Develop a more focused marketing approach</li>
          </ul>
        </div>
      </div>
      
      {/* Questionnaire Loader Component */}
      {!submissionResult ? (
        <QuestionnaireLoader 
          title="Marketing Research Questionnaire" 
          description="Please select and complete one of our marketing research questionnaires to help us understand your business needs."
          // You can specify which questionnaires to show
          specificQuestionnaires={[
            'lead-generation_strategy-questionnaire-01.yaml',
            '00-strategy-questionnaire.yaml',
            '03-ideal-customer-profile.yaml'
          ]}
          // You can set a default questionnaire to load automatically
          defaultQuestionnaire="lead-generation_strategy-questionnaire-01.yaml"
          // Handle submission
          onSubmit={handleSubmit}
          // Don't show the local save button
          showLocalSave={false}
          // Hide the generate strategy button at the end of the questionnaire
          hideGenerateStrategyButton={true}
        />
      ) : (
        <div className="bg-green-50 p-6 rounded-lg shadow-md border border-green-200">
          <h2 className="text-xl font-semibold mb-4 text-green-700">
            <svg className="inline-block w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
            </svg>
            {submissionResult.message}
          </h2>
          <p className="mb-4">
            We've received your responses for the "{submissionResult.data.questionnaire}" questionnaire.
            Our team will analyze your information and provide tailored recommendations.
          </p>
          
          <div className="flex flex-wrap gap-4 mt-6">
            <button 
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
              onClick={() => setSubmissionResult(null)}
            >
              Complete Another Questionnaire
            </button>
            
            <button
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition"
              onClick={() => {
                // Store the responses in sessionStorage for viewing
                sessionStorage.setItem('questionnaire_view_responses', JSON.stringify(submissionResult.data));
                // Navigate to the responses view page
                navigate('/responses');
              }}
            >
              View Your Responses
            </button>
          </div>
          
          {/* Strategy Generation Section */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <h3 className="text-lg font-semibold mb-3 text-blue-700">Generate Marketing Strategy</h3>
            <p className="mb-4">
              Ready to turn your questionnaire responses into an actionable marketing strategy?
              Click the button below to generate a comprehensive strategy tailored to your business needs.
            </p>
            <button
              className="px-6 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 transition flex items-center"
              onClick={() => {
                // Store the responses in sessionStorage for the strategy page
                sessionStorage.setItem('questionnaire_responses', JSON.stringify(submissionResult.data));
                // Navigate to the strategy page
                navigate('/strategy');
              }}
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
              </svg>
              Generate Strategy
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default QuestionnairePage;
