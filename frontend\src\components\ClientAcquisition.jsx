import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import QuestionnaireLoader from './QuestionnaireLoader';
import AIAgent from './AIAgent';
import { getQuestionnaireResponses } from '../supabase/client';

function ClientAcquisition() {
  const navigate = useNavigate();
  const [activeQuestionnaire, setActiveQuestionnaire] = useState(null);
  const [submissionResult, setSubmissionResult] = useState(null);
  const [generatingStrategy, setGeneratingStrategy] = useState(false);
  const [completedQuestionnaires, setCompletedQuestionnaires] = useState({
    leadGeneration: false,
    customerAcquisitionStrategy: false,
    marketingCampaigns: false,
    customerOnboarding: false
  });

  const questionnaireConfigs = {
    leadGeneration: {
      title: 'Lead Generation Questionnaire',
      description: 'Optimize your lead generation strategies to attract and convert potential customers.',
      files: ['lead-generation-strategy.yaml'],
      defaultFile: 'lead-generation-strategy.yaml'
    },
    customerAcquisitionStrategy: {
      title: 'Customer Acquisition Strategy Questionnaire',
      description: 'Develop comprehensive strategies to acquire new customers effectively.',
      files: ['customer-acquisition-strategy.yaml'],
      defaultFile: 'customer-acquisition-strategy.yaml'
    },
    marketingCampaigns: {
      title: 'Marketing Campaigns Questionnaire',
      description: 'Create and optimize marketing campaigns that drive customer acquisition.',
      files: ['marketing-campaigns.yaml'],
      defaultFile: 'marketing-campaigns.yaml'
    },
    customerOnboarding: {
      title: 'Customer Onboarding Questionnaire',
      description: 'Assess and improve your customer onboarding process to increase retention and customer lifetime value.',
      files: ['customer-onboarding.yaml'],
      defaultFile: 'customer-onboarding.yaml'
    }
  };

  const [actualCompletedQuestionnaires, setActualCompletedQuestionnaires] = useState({
    leadGeneration: false,
    customerAcquisitionStrategy: false,
    marketingCampaigns: false,
    customerOnboarding: false
  });

  useEffect(() => {
    const checkCompletedQuestionnaires = async () => {
      const completed = {};
      for (const key of Object.keys(questionnaireConfigs)) {
        try {
          const { data, error } = await getQuestionnaireResponses(key);
          completed[key] = !error && data && Object.keys(data.responses || {}).length > 0;
        } catch (err) {
          const storedData = localStorage.getItem(`questionnaire_responses_${key}`);
          completed[key] = !!storedData;
        }
      }
      setActualCompletedQuestionnaires(completed);
      setCompletedQuestionnaires(completed);
    };

    checkCompletedQuestionnaires();
  }, []);

  const handleSubmit = async (data) => {
    try {
      setSubmissionResult({
        success: true,
        message: 'Questionnaire submitted successfully!',
        data: data
      });

      const updatedCompleted = { ...completedQuestionnaires };
      if (activeQuestionnaire) {
        updatedCompleted[activeQuestionnaire] = true;
      }
      setCompletedQuestionnaires(updatedCompleted);
      setActualCompletedQuestionnaires(updatedCompleted);
      setActiveQuestionnaire(null);
    } catch (error) {
      console.error('Error submitting questionnaire:', error);
      setSubmissionResult({
        success: false,
        message: 'Failed to submit questionnaire. Please try again.',
        data: null
      });
    }
  };

  const handleGenerateStrategy = async (data) => {
    setGeneratingStrategy(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 3000));
      setSubmissionResult({
        success: true,
        message: 'Strategy generated successfully!',
        data: data,
        isStrategy: true
      });
      setActiveQuestionnaire(null);
    } catch (error) {
      console.error('Error generating strategy:', error);
      setSubmissionResult({
        success: false,
        message: 'Failed to generate strategy. Please try again.',
        data: null
      });
    } finally {
      setGeneratingStrategy(false);
    }
  };

  const contextPrompt = `You are a Client Acquisition Assistant specializing in helping businesses develop effective strategies to attract and convert new customers. Your expertise includes:

- Lead generation strategies and tactics
- Customer acquisition funnels and conversion optimization
- Digital marketing and advertising strategies
- Sales process optimization
- Customer onboarding and retention strategies
- Market research and target audience identification
- Competitive analysis and positioning
- ROI measurement and analytics

Provide practical, actionable advice tailored to the user's specific business context and goals. Focus on proven strategies that can help businesses grow their customer base effectively and sustainably.`;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="w-full max-w-6xl mx-auto">
          <div className="bg-white rounded-lg shadow-md p-8 mb-8">
            <h1 className="raleway-title-h1 mb-6 text-center text-amber-800">CLIENT ACQUISITION</h1>
            <p className="body-text text-center mb-8 max-w-3xl mx-auto">
              Develop comprehensive strategies to attract, convert, and onboard new customers. 
              Our questionnaires help you optimize your client acquisition process from lead generation to customer success.
            </p>

            {/* Questionnaire Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              {Object.entries(questionnaireConfigs).map(([key, config]) => (
                <div key={key} className="bg-gray-50 p-6 rounded-lg border border-gray-200 hover:border-amber-300 transition-colors">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="raleway-title-h3 text-amber-800">{config.title.replace(' Questionnaire', '')}</h3>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      actualCompletedQuestionnaires[key]
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {actualCompletedQuestionnaires[key] ? 'Completed' : 'Not Started'}
                    </span>
                  </div>
                  <p className="body-text mb-4">{config.description}</p>
                  <button
                    className="w-full px-4 py-2 bg-amber-600 text-white rounded hover:bg-amber-700 transition"
                    onClick={() => setActiveQuestionnaire(key)}
                  >
                    {actualCompletedQuestionnaires[key] ? 'Review & Update' : 'Start Questionnaire'}
                  </button>
                </div>
              ))}
            </div>

            {/* Questionnaire Completion Status */}
            <div className="mt-8 p-4 bg-gray-50 rounded-lg">
              <h3 className="raleway-title-h3 mb-4 text-gray-800">Questionnaire Completion Status</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(questionnaireConfigs).map(([key, config]) => (
                  <div key={key} className="flex items-center justify-between p-3 bg-white rounded border">
                    <span className="body-text">{config.title.replace(' Questionnaire', '')}</span>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      actualCompletedQuestionnaires[key]
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {actualCompletedQuestionnaires[key] ? 'Completed' : 'Not Started'}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Strategy Generation Section */}
            <div className="mt-8 p-6 bg-white rounded-lg border border-amber-200 shadow-md">
              <h4 className="raleway-title-h4 mb-3 text-amber-800">GENERATE CLIENT ACQUISITION STRATEGY</h4>
              <p className="body-text mb-4">
                Ready to turn your questionnaire responses into an actionable client acquisition strategy?
                Click the button below to generate a comprehensive strategy tailored to your business needs.
              </p>
              <div className="flex flex-wrap gap-4">
                <button
                  className="px-6 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 transition flex items-center"
                  onClick={() => {
                    const responseData = {
                      questionnaire: 'Client Acquisition Questionnaires',
                      responses: completedQuestionnaires,
                      timestamp: new Date().toISOString()
                    };
                    sessionStorage.setItem('questionnaire_responses', JSON.stringify(responseData));
                    navigate('/strategy');
                  }}
                  disabled={!Object.values(completedQuestionnaires).some(value => value)}
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                  </svg>
                  Generate Strategy
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Questionnaire Section */}
        {activeQuestionnaire && (
          <div className="mb-8 p-6 bg-white rounded-lg border border-gray-200 shadow-md">
            <div className="flex justify-end mb-4">
              <button
                onClick={() => setActiveQuestionnaire(null)}
                className="text-gray-500 hover:text-gray-700"
                aria-label="Close questionnaire"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <QuestionnaireLoader
              key={activeQuestionnaire}
              title={questionnaireConfigs[activeQuestionnaire].title}
              description={questionnaireConfigs[activeQuestionnaire].description}
              specificQuestionnaires={questionnaireConfigs[activeQuestionnaire].files}
              defaultQuestionnaire={questionnaireConfigs[activeQuestionnaire].defaultFile}
              onSubmit={handleSubmit}
              onGenerateStrategy={handleGenerateStrategy}
              showLocalSave={false}
              hideGenerateStrategyButton={true}
              hideQuestionnaireSelector={true}
            />
          </div>
        )}

        {/* Questionnaire Success Message Popup */}
        {submissionResult && (
          <div className="fixed inset-0 flex items-center justify-center z-50">
            <div className="absolute inset-0 bg-black opacity-30"></div>
            <div className="bg-white p-6 rounded-lg shadow-xl border border-green-200 z-10 max-w-md w-full mx-4">
              <div className="flex items-center justify-center mb-4 text-green-500">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold mb-2 text-center text-green-700">
                {submissionResult.message}
              </h2>
              <button
                className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition w-full"
                onClick={() => setSubmissionResult(null)}
              >
                Close
              </button>
            </div>
          </div>
        )}

        {/* Strategy Generation Loading */}
        {generatingStrategy && (
          <div className="bg-blue-50 p-6 rounded-lg shadow-md border border-blue-200 mb-8 text-center">
            <div className="animate-pulse flex flex-col items-center">
              <svg className="animate-spin h-10 w-10 text-blue-600 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <h2 className="text-xl font-semibold mb-2 text-blue-700">Generating Your Strategy</h2>
              <p className="body-text">We're analyzing your responses and creating a customized client acquisition strategy...</p>
            </div>
          </div>
        )}

        {/* Client Acquisition AI Agent */}
        <AIAgent
          title="Client Acquisition Assistant"
          description="Ask questions about client acquisition strategies, techniques, and best practices."
          contextPrompt={contextPrompt}
        />
      </div>
    </div>
  );
}

export default ClientAcquisition;
