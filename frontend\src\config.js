/**
 * Configuration for the application
 * This file manages environment-specific settings
 */

// Determine if we're in production based on the URL
const isProduction = window.location.hostname !== 'localhost';

// API base URL - use relative URLs in production for Vercel deployment (questionnaires, responses, etc.)
const API_BASE_URL = isProduction ? '/api' : 'http://localhost:3000/api';

// Railway backend URL for LLM operations
const RAILWAY_API_BASE_URL = isProduction
  ? 'https://op-market-research-tool-production-93d7.up.railway.app'
  : 'http://localhost:8000';

export default {
  API_BASE_URL,
  RAILWAY_API_BASE_URL,
  endpoints: {
    // Questionnaire endpoints (Vercel)
    questionnaire: {
      list: `${API_BASE_URL}/questionnaire/list`,
      get: (id) => `${API_BASE_URL}/questionnaire/${id}`,
      save: `${API_BASE_URL}/questionnaire/save`,
      generate: `${API_BASE_URL}/questionnaire/generate`,
    },
    // YAML endpoints (Vercel)
    yaml: {
      list: `${API_BASE_URL}/yaml/list`,
      get: (file) => `${API_BASE_URL}/yaml/${file}`,
    },
    // Response endpoints (Vercel)
    responses: {
      list: `${API_BASE_URL}/responses/list`,
      get: (id) => `${API_BASE_URL}/responses/${id}`,
      save: `${API_BASE_URL}/responses/save`,
      download: (id) => `${API_BASE_URL}/responses/download/${id}`,
    },
    // AI model endpoints (Railway)
    gemini: {
      list: `${API_BASE_URL}/gemini/list`, // Keep list on Vercel if needed
      ask: `${RAILWAY_API_BASE_URL}/api/gemini`,
    },
    openai: {
      ask: `${RAILWAY_API_BASE_URL}/api/openai`,
    },
    deepseek: {
      ask: `${RAILWAY_API_BASE_URL}/api/deepseek`,
    },
    anthropic: {
      ask: `${RAILWAY_API_BASE_URL}/api/anthropic`,
    },
    groq: {
      ask: `${RAILWAY_API_BASE_URL}/api/groq`,
    }
  }
};
